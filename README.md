## About 0x15

I'm an independent Web3 Security Researcher competing on <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and Codehawks. Skilled in Rust, Go, Move, Solidity, and Cairo, I specialize in identifying vulnerabilities in blockchain protocols.

For collabs or security audits, reach out on X [@0x15_eth](https://x.com/0x15_eth).

## Contests
### 2025

### Cantina
| Contest | Category | Language | Report |
| -------- | ------- |  -------- | -------- | 
| [Tally]() | Liquid Staking Governance Token | Solidity | [source](contests/Tally.md) |
| [PumpSwap](https://cantina.xyz/competitions/19c5a5a6-f68d-4da8-b185-3f28c7f97bc1) | AMM | Rust | [source](contests/PumpSwap.md)|
| [Gamma Strategies](https://cantina.xyz/competitions/aaf79192-6ea7-4b1e-aed7-3d23212dd0f1) | Uniswap v4 |  Solidity | [source](contests/Gamma.md) |
[EigenLayer](https://cantina.xyz/competitions/e7af4986-183d-4764-8bd2-1d6b47f87d99) | Staking | Solidity | [source](contests/Eigenlayer.md) |
| [LiquityV2](https://cantina.xyz/competitions/d86632df-ab33-4448-8198-64955eae6712) | CDP | Solidity | [source](contests/LiquityV2.md) |
| [Infinifi](https://cantina.xyz/competitions/2ac7f906-1661-47eb-bfd6-519f5db0d36b) | Yield Farming | Solidity | [source](contests/Infinifi.md) |
| [Metropolis](https://cantina.xyz/competitions/076935b1-2706-48c6-bf0a-b3656aa24194) | Liquidity Management | Solidity | [source](contests/Metropolis.md) |
| [Mighty Finance](https://cantina.xyz/competitions/616d8bb4-16ce-4ca9-9ce9-5b99d6e146ef) | Liquidity Management  | Solidity | [source](contests/mightyfinance.md) | 
| [Mezo](https://cantina.xyz/code/e757364c-1f68-4ec5-94f6-c6b3c2e80c6d) | Lending   | Solidity, Go | [source](contests/mezo.md) | 
| [StabilityDAO](https://cantina.xyz/competitions/e1c0be8d-0c3d-485a-a446-a582beb120b1) | Yield Aggregator  | Solidity | [source](contests/stability.md) | 
| [Primev](https://cantina.xyz/competitions/e92be0b9-b4f2-4bf2-9544-ae285fcfc02d) | MEV Relay/Commitment-layer   | Solidity | [source](contests/primev.md) | 
| [Alchemix](https://cantina.xyz/code/e68909e6-3491-4a94-a707-ecf0c89cf72a/overview) | Lending and Borrowing   | Solidity | [source](contests/Alchemix.md) | 
| [Telcoin](https://cantina.xyz/code/26d5255b-6f68-46cf-be55-81dd565d9d16/overview) | Blockchain/DLT   | Rust | [source](contests/Telcoin.md) | 
| [Aave-aptos](https://cantina.xyz/code/ad445d42-9d39-4bcf-becb-0c6c8689b767/overview) | Lending   | Move | [source](contests/aptos.md) | 
| [Jigsaw](https://cantina.xyz/competitions/7a40c849-0b35-4128-b084-d9a83fd533ea) | Lending/CDP   | Move | [source](contests/Jigsaw.md) | 

### Code4rena
| Contest | Category | Language | Report |
| -------- | ------- |  -------- | -------- |
| [Kinetiq](https://code4rena.com/audits/2025-04-kinetiq) | Liquid Staking | Solidity | [source](contests/Kinetiq.md) |
| [Cabal Liquid Staking Token](https://code4rena.com/audits/2025-04-cabal-liquid-staking-token) | Liquid Staking |  Move |[source](contests/cabal.md) |
| [Upside](https://code4rena.com/audits/2025-05-upside) | Bonding Curve |  Solidity |[source](contests/upside.md) |

### Sherlock
| Contest | Category | Language | Report |
| -------- | ------- |  -------- | --------
| [Yieldoor](https://audits.sherlock.xyz/contests/791/report) | Yield Farming | Solidity | [source](contests/Yieldoor.md) |
| [Rova](https://audits.sherlock.xyz/contests/498/report) | Token Launchpad|  Solidity | [source](contests/Rova.md) |
| [LayerEdge](https://audits.sherlock.xyz/contests/952/report) | Staking |  Solidity | [source](contests/LayerEdge.md) |
| [Yearn yBOLD](https://audits.sherlock.xyz/contests/977/report) | Yield Optimizer|  Solidity | [source](contests/ybold.md) |

### Codehawks
| Contest | Category | Language | Report |
| -------- | ------- |  -------- | --------
| [RAAC](https://codehawks.cyfrin.io/c/2025-02-raac) | RWA Tokenization | Solidity | [source](contests/raac.md) |
