## Jigsaw
[Contest details](https://cantina.xyz/code/7a40c849-0b35-4128-b084-d9a83fd533ea/overview)

### [High-01] Missing balance snapshot in `ElixirStrategy.withdraw()` causes full balance drain

**Description**
In the `ElixirStrategy.withdraw()` function, the variable `params.balanceBefore` is never initialized before performing the Uniswap swap. As a result, when computing

```solidity
params.withdrawnAmount = IERC20(tokenIn).balanceOf(_recipient) - params.balanceBefore;
```
`params.balanceBefore` remains zero, so `withdrawnAmount` incorrectly equals the user’s entire USDT balance rather than just the amount generated by the swap.

As a result any user with USDT outside the strategy can lose their entire USDT balance whenever they withdraw even a tiny share. Fees and internal accounting are calculated against this inflated `withdrawnAmount`, causing wildly incorrect state updates.

**Recommendation**

Before performing the swap back to USDT, snapshot the recipient’s current balance into `params.balanceBefore`. Then, after the swap, compute with`drawnAmount as the delta. This ensures only the USDT received from the swap (and corresponding fees) is accounted for, preventing unintended full-balance withdrawals.



### [Low-01] Double floor rounded share ratio causes fees on principal

**Description**

Across strategies (e.g., Aave, Elixir, Reservoir), the user’s withdrawable investment portion is computed by first calculating a floored share ratio and then applying it to their invested principal:
```solidity
// 1) Compute floor‐rounded share ratio
params.shareRatio = OperationsLib.getRatio({
    numerator: params.shares,
    denominator: params.totalShares,
    precision: params.shareDecimals,
    rounding: OperationsLib.Rounding.Floor
});

// 2) Apply ratio to principal
params.investment = (recipients[_recipient].investedAmount * params.shareRatio)
                  / (10 ** params.shareDecimals);

```
Because both steps truncate any fractional remainder, a small slice of the user’s principal is left out of `params.investment` and implicitly classified as yield. The performance‐fee logic then deducts fees on that phantom yield even though no actual gain occurred.

Large liquidity providers breaking up their exit into many small withdrawals will systematically pay performance fees on their own principal.

**Recommendation**

Compute the invested principal exactly (rounding down only where unavoidable) and classify only true gains as yield.